# PDF Chat Assistant

A comprehensive AI-powered system for chatting with PDF documents using RAG (Retrieval-Augmented Generation) architecture.

## Project Structure
```
thesis/
├── core/                # Core processing modules
├── web_server/          # FastAPI backend
├── web_client/          # React frontend
├── experiment/          # Research experiments
├── parser/              # Document processing
├── data/                # Datasets and storage
└── README.md            # This file
```
## Prerequisites

- Python 3.10+
- Node.js 18+
- OpenAI API key
- Qdrant vector database

## Environment Setup

**Download datasets:**
```bash
sh download_peerqa.sh
python3.10 -m data.download_openreview
python3.10 -m data.download_nlpeer
python3.10 -m data.download_egu
```

**Download models:**

```bash
cd parser
sh download_model.sh
```

**Install Python dependencies:**
```bash
pip install -r requirements.txt
```

**Set up environment variables:** 
copy .env.sample to .env and fill in the values

**Install Node.js dependencies:**
```bash
cd web_client
npm install
cd ..
```

## Experimental Run

Run different experiments
```bash
python3.10 -m experiment.run_golden_evidence
python3.10 -m experiment.run_rag_dense
python3.10 -m experiment.run_rag_full
```
NotebookLM is not included in the experiments. It is a separate model that we used for comparison. We have include the results in the experiment folder.

For evaluation metrics, please refer to the original dataset's author's github repo (https://github.com/UKPLab/PeerQA/tree/main). Note that there is dependency conflicts between two evaluation scripts. You may use venv or resolve the conflicts manually.

The evaluated results are included in the experiment folder.

## Development Server

### Start Backend Server
```bash
# From project root
python3.10 -m web_server.start
```
The backend will be available at `http://localhost:8000`

### Start Frontend Development Server
```bash
# In a new terminal
cd web_client
npm run dev
```
The frontend will be available at `http://localhost:5173`



