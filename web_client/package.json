{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "serve": "serve dist -p 8501", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@tailwindcss/typography": "^0.5.15", "class-variance-authority": "^0.7.0", "classix": "^2.2.0", "clsx": "^2.1.1", "framer-motion": "^11.11.13", "geist": "^1.3.1", "lucide-react": "^0.456.0", "pdfjs-dist": "^5.3.93", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-pdf": "^10.1.0", "react-router-dom": "^6.28.0", "remark-gfm": "^4.0.0", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^20.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "copy-webpack-plugin": "^13.0.1", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "husky": "^9.1.7", "postcss": "^8.4.49", "serve": "^14.2.4", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}