import React, { useState, useCallback } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  ZoomIn,
  ZoomOut,
  RotateCw,
  Download,
  Maximize2,
  Minimize2,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Set up PDF.js worker - will be configured in main.tsx

interface PDFViewerProps {
  url: string;
  className?: string;
  pdfName?: string;
  showControls?: boolean;
  onLoadSuccess?: (pdf: any) => void;
  onLoadError?: (error: Error) => void;
}

export function PDFViewer({ url, className, pdfName, showControls = true, onLoadSuccess, onLoadError }: PDFViewerProps) {
  const [numPages, setNumPages] = useState<number>(0);
  const [scale, setScale] = useState<number>(1.0);
  const [rotation, setRotation] = useState<number>(0);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
    setError(null);
    onLoadSuccess?.({ numPages });
  }, [onLoadSuccess]);

  const onDocumentLoadError = useCallback((error: Error) => {
    setIsLoading(false);
    setError(error.message);
    onLoadError?.(error);
  }, [onLoadError]);



  const zoomIn = () => {
    setScale(prev => Math.min(3.0, prev + 0.2));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(0.5, prev - 0.2));
  };

  const rotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  const downloadPDF = () => {
    const link = document.createElement('a');
    link.href = url;
    link.download = 'document.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };



  if (error) {
    return (
      <div className={cn("flex items-center justify-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg", className)}>
        <div className="text-center">
          <div className="text-red-500 mb-2">Failed to load PDF</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "flex flex-col bg-white dark:bg-gray-900 border overflow-hidden",
      isFullscreen && "fixed inset-0 z-50",
      className,
      "rounded-lg"
    )}>
      {/* Toolbar */}
      {showControls && (
        <div className="flex items-center justify-between p-3 border-b bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 dark:text-gray-400 truncate max-w-xs">
              {pdfName || (numPages ? `${numPages} pages` : 'Loading...')}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={zoomOut} disabled={isLoading}>
              <ZoomOut className="h-4 w-4" />
            </Button>

            <span className="text-sm text-gray-600 dark:text-gray-400 min-w-12 text-center">
              {Math.round(scale * 100)}%
            </span>

            <Button variant="outline" size="sm" onClick={zoomIn} disabled={isLoading}>
              <ZoomIn className="h-4 w-4" />
            </Button>

            <Button variant="outline" size="sm" onClick={rotate} disabled={isLoading}>
              <RotateCw className="h-4 w-4" />
            </Button>

            <Button variant="outline" size="sm" onClick={downloadPDF} disabled={isLoading}>
              <Download className="h-4 w-4" />
            </Button>

            <Button variant="outline" size="sm" onClick={toggleFullscreen}>
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      )}

      {/* PDF Content */}
      <ScrollArea className="flex-1">
        <div className="flex flex-col items-center p-4 gap-4">
          {isLoading && (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading PDF...</span>
            </div>
          )}

          <Document
            file={url}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading=""
            className="max-w-full"
          >
            {Array.from(new Array(numPages), (_, index) => (
              <div key={`page_${index + 1}`} className="mb-4">
                <Page
                  pageNumber={index + 1}
                  scale={scale}
                  rotate={rotation}
                  className="shadow-lg border"
                  loading=""
                  renderTextLayer={true}
                  renderAnnotationLayer={true}
                />
              </div>
            ))}
          </Document>
        </div>
      </ScrollArea>
    </div>
  );
}
