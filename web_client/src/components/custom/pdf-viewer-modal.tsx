import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { PDFViewer } from './pdf-viewer';
import { Button } from '@/components/ui/button';
import apiService from '@/services/api';

interface PDFViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  pdfId: string;
  pdfName: string;
}

export function PDFViewerModal({ isOpen, onClose, pdfId, pdfName }: PDFViewerModalProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && pdfId) {
      loadPDFUrl();
    }
  }, [isOpen, pdfId]);

  const loadPDFUrl = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const url = await apiService.getPDFViewUrl(pdfId);
      setPdfUrl(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load PDF');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setPdfUrl(null);
    setError(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl h-[90vh] flex flex-col p-0">
        <div className="flex-1 overflow-hidden">
          {isLoading && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white mx-auto mb-4"></div>
                <p>Loading PDF...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-red-500 mb-2">Failed to load PDF</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
                <Button 
                  variant="outline" 
                  onClick={loadPDFUrl}
                  className="mt-4"
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {pdfUrl && !isLoading && !error && (
            <PDFViewer
              url={pdfUrl}
              pdfName={pdfName}
              className="h-full border-0 rounded-none"
              showControls={true}
              onClose={onClose}
              onLoadError={(error) => setError(error.message)}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
