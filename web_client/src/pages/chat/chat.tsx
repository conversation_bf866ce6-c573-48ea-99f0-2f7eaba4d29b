import { ChatInput } from "@/components/custom/chatinput";
import { PreviewMessage, ThinkingMessage, TypingMessage } from "../../components/custom/message";
import { useScrollToBottom } from '@/components/custom/use-scroll-to-bottom';
import { useState, useRef, useEffect } from "react";
import { message, RelevanceChunk, PDF } from "../../interfaces/interfaces"
import { Overview } from "@/components/custom/overview";
import { Header } from "@/components/custom/header";
import { PDFSidebar } from "@/components/custom/pdf-sidebar";
import { RelevanceChunksSidebar } from "@/components/custom/relevance-chunks-sidebar";
import {v4 as uuidv4} from 'uuid';
import apiService from '@/services/api';

export function Chat() {
  const [messages, setMessages] = useState<message[]>([]);
  const [question, setQuestion] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [messagesContainerRef, messagesEndRef] = useScrollToBottom<HTMLDivElement>(isLoading, messages.length);
  const [isPDFSidebarOpen, setIsPDFSidebarOpen] = useState<boolean>(false);
  const [isChunksSidebarOpen, setIsChunksSidebarOpen] = useState<boolean>(false);
  const [relevanceChunks, setRelevanceChunks] = useState<RelevanceChunk[]>([]);
  const [currentQuery, setCurrentQuery] = useState<string>("");
  const [selectedPDFs, setSelectedPDFs] = useState<string[]>(() => {
    // Load selected PDFs from localStorage on initialization
    const saved = localStorage.getItem('selected-pdfs');
    return saved ? JSON.parse(saved) : [];
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  // Save selected PDFs to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('selected-pdfs', JSON.stringify(selectedPDFs));
  }, [selectedPDFs]);

  // Removed auto-open chunks sidebar behavior
  // Users can manually open the sidebar using the header button

  const cleanupRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  };

async function handleSubmit(text?: string) {
  if (isLoading) return;

  // Require document selection
  if (selectedPDFs.length === 0) {
    return;
  }

  const messageText = text || question;

  // Clear the input immediately
  setQuestion("");

  setIsLoading(true);
  cleanupRequest();

  // Store the current query for the chunks sidebar
  setCurrentQuery(messageText);
  // Clear previous chunks
  setRelevanceChunks([]);

  const traceId = uuidv4();
  setMessages(prev => [...prev, { content: messageText, role: "user", id: traceId }]);

  try {
    // Create abort controller for this request
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    // Send request to streaming endpoint using API service
    console.log("Sending chat message:", { message: messageText, paper_ids: selectedPDFs, trace_id: traceId });

    const response = await apiService.sendChatMessage(messageText, selectedPDFs, traceId, abortController.signal);

    console.log("Response status:", response.status);
    console.log("Response headers:", response.headers);

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body reader available");
    }

    // Stream the response
    let buffer = "";
    let chunksProcessed = false;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += new TextDecoder().decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || ""; // Keep incomplete line in buffer

      for (const line of lines) {
        // Check for chunks data
        if (line.startsWith("[CHUNKS]") && line.endsWith("[/CHUNKS]")) {
          try {
            const chunksJson = line.substring(8, line.length - 9); // Remove [CHUNKS] and [/CHUNKS]
            const chunksData = JSON.parse(chunksJson);
            setRelevanceChunks(chunksData.chunks || []);
            chunksProcessed = true;
            console.log("Received chunks:", chunksData.chunks);
            continue;
          } catch (error) {
            console.error("Error parsing chunks data:", error);
          }
        }

        if (line === "[END]") {
          setIsLoading(false);
          cleanupRequest();
          return;
        }

        // Process regular message content
        setMessages(prev => {
          const lastMessage = prev[prev.length - 1];
          const newContent = lastMessage?.role === "assistant"
            ? lastMessage.content + (line? line.replace("[EL]", "\n") : "\n")
            : (line? line.replace("[EL]", "\n") : "\n");

          const newMessage = { content: newContent, role: "assistant", id: traceId };
          return lastMessage?.role === "assistant"
            ? [...prev.slice(0, -1), newMessage]
            : [...prev, newMessage];
        });
      }
    }

  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log("Request was aborted");
    } else {
      console.error("Streaming error:", error);
    }
    setIsLoading(false);
    cleanupRequest();
  }
}

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background relative">
      {/* Fixed Transparent Header */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-transparent">
        <Header
          onTogglePDFSidebar={() => setIsPDFSidebarOpen(!isPDFSidebarOpen)}
          onToggleChunksSidebar={() => setIsChunksSidebarOpen(!isChunksSidebarOpen)}
          hasChunks={relevanceChunks.length > 0}
        />
      </div>

      <PDFSidebar
        isOpen={isPDFSidebarOpen}
        onClose={() => setIsPDFSidebarOpen(false)}
        selectedPDFs={selectedPDFs}
        onPDFSelectionChange={setSelectedPDFs}
      />

      <RelevanceChunksSidebar
        isOpen={isChunksSidebarOpen}
        onClose={() => setIsChunksSidebarOpen(false)}
        chunks={relevanceChunks}
        query={currentQuery}
      />

      <div className={`flex flex-col min-w-0 flex-1 transition-all duration-200 ${
        isPDFSidebarOpen ? 'ml-[400px]' : ''
      } ${
        isChunksSidebarOpen ? 'mr-[400px]' : ''
      }`}>
        {/* Scrollable Messages Area with top/bottom padding for fixed elements */}
        <div className="flex flex-col min-w-0 gap-6 flex-1 overflow-y-scroll pt-16 pb-20" ref={messagesContainerRef}>
          {messages.length == 0 && (
            <Overview
              hasSelectedDocument={selectedPDFs.length > 0}
            />
          )}
          {messages.map((message, index) => {
            // Show typing effect for the last assistant message while loading
            const isLastAssistantMessage = index === messages.length - 1 && message.role === "assistant";
            const showTypingEffect = isLoading && isLastAssistantMessage;

            return showTypingEffect ? (
              <TypingMessage key={index} message={message} isTyping={true} />
            ) : (
              <PreviewMessage key={index} message={message} />
            );
          })}
          {isLoading && messages.length === 0 && <ThinkingMessage />}
          {isLoading && messages.length > 0 && messages[messages.length - 1].role === "user" && <ThinkingMessage />}
          <div className="min-w-[24px] min-h-[64px]"/> {/* Padding */}
          <div ref={messagesEndRef} className="shrink-0"/>
        </div>

        <div className={`fixed bottom-0 z-40 bg-background transition-all duration-200 ${
          isPDFSidebarOpen ? 'left-[400px]' : 'left-0'
        } ${
          isChunksSidebarOpen ? 'right-[400px]' : 'right-0'
        }`}>
          <div className="flex mx-auto px-4 py-4 gap-2 w-full md:max-w-3xl">
            <ChatInput
              question={question}
              setQuestion={setQuestion}
              onSubmit={handleSubmit}
              isLoading={isLoading}
              disabled={selectedPDFs.length === 0}
              placeholder={selectedPDFs.length === 0 ? "Please select a PDF document to start chatting..." : "Send a message..."}
            />
          </div>
        </div>
      </div>
    </div>
  );
};