import json
import boto3
from time import sleep

from core import config, logger
from botocore.config import Config

READ_TIMEOUT = 900

class LambdaService:
  def __init__(self):
    self.access_key = config.getenv("AWS_ACCESS_KEY_ID")
    self.secret_key = config.getenv("AWS_SECRET_ACCESS_KEY")
    self.region = config.getenv("AWS_REGION")
    self.session = boto3.Session(
      aws_access_key_id=self.access_key,
      aws_secret_access_key=self.secret_key,
      region_name=self.region
    )
    self.lambdaclient = self.session.client("lambda", config=Config(read_timeout=READ_TIMEOUT))
    self._check_auth()
  
  def _check_auth(self):
    try:
      logger.info("Checking Lambda authentication")
      self.lambdaclient.list_functions()
    except Exception as e:
      logger.error(f"Failed to connect to Lambda: {e}")
      raise e
  
  def invoke(self, function_name: str, payload: dict):
    logger.info(f"Invoking Lambda function {function_name}")
    try: 
      response = self.lambdaclient.invoke(
        FunctionName=function_name,
        InvocationType="RequestResponse",
        Payload=bytes(json.dumps(payload), encoding="utf-8")
      )
      logger.info(f"Lambda function {function_name} invoked successfully")
      return json.load(response["Payload"])
    except Exception as e:
      if "Lambda is initializing your function" in str(e):
        logger.info("Lambda is initializing function, retrying in 1 seconds")
        sleep(1)
        return self.invoke(function_name, payload)
      else: 
        logger.error(f"Error invoking Lambda function {function_name}: {e}")
      raise e


